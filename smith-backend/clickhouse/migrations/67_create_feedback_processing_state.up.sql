-- Create a table to track feedback processing state for the cron job
-- This table helps handle race conditions and tracks which feedbacks have been processed
CREATE TABLE IF NOT EXISTS feedback_processing_state {{ON_CLUSTER}}
(
    id String COMMENT 'Unique identifier for the processing batch',
    last_processed_time DateTime64(6, 'UTC') COMMENT 'Last time feedbacks were successfully processed',
    processing_start_time DateTime64(6, 'UTC') COMMENT 'When this processing batch started',
    processing_end_time Nullable(DateTime64(6, 'UTC')) COMMENT 'When this processing batch completed (NULL if still running)',
    status LowCardinality(String) COMMENT 'Status: RUNNING, COMPLETED, FAILED',
    feedbacks_processed UInt64 DEFAULT 0 COMMENT 'Number of feedbacks processed in this batch',
    feedbacks_failed UInt64 DEFAULT 0 COMMENT 'Number of feedbacks that failed to process',
    error_message Nullable(String) COMMENT 'Error message if processing failed',
    created_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'When this record was created',
    modified_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'When this record was last modified'
)
ENGINE = {{REPLACING_ENGINE(modified_at)}}
ORDER BY (id)
SETTINGS index_granularity = 8192;

-- Create a table to track individual feedback processing status
-- This helps with race condition handling and retry logic
CREATE TABLE IF NOT EXISTS feedback_processing_items {{ON_CLUSTER}}
(
    feedback_id UUID COMMENT 'Feedback ID from feedbacks_rmt table',
    batch_id String COMMENT 'Processing batch ID',
    status LowCardinality(String) COMMENT 'Status: PENDING, PROCESSED, FAILED, SKIPPED',
    run_id UUID COMMENT 'Run ID associated with the feedback',
    tenant_id UUID COMMENT 'Tenant ID for partitioning',
    processing_time DateTime64(6, 'UTC') COMMENT 'When this feedback was processed',
    error_message Nullable(String) COMMENT 'Error message if processing failed',
    retry_count UInt8 DEFAULT 0 COMMENT 'Number of times processing was retried',
    created_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'When this record was created',
    modified_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'When this record was last modified'
)
ENGINE = {{REPLACING_ENGINE(modified_at)}}
ORDER BY (tenant_id, feedback_id, batch_id)
SETTINGS index_granularity = 8192;

-- Add indexes for efficient querying
ALTER TABLE feedback_processing_state ADD INDEX IF NOT EXISTS sk_mm_last_processed_time last_processed_time TYPE minmax GRANULARITY 1;
ALTER TABLE feedback_processing_state ADD INDEX IF NOT EXISTS sk_mm_processing_start_time processing_start_time TYPE minmax GRANULARITY 1;

ALTER TABLE feedback_processing_items ADD INDEX IF NOT EXISTS sk_mm_processing_time processing_time TYPE minmax GRANULARITY 1;
ALTER TABLE feedback_processing_items ADD INDEX IF NOT EXISTS idx_tenant_id_bloom tenant_id TYPE bloom_filter(0.01) GRANULARITY 1;
ALTER TABLE feedback_processing_items ADD INDEX IF NOT EXISTS idx_run_id_bloom run_id TYPE bloom_filter(0.01) GRANULARITY 1;
