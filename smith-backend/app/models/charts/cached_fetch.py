"""Functions for caching chart data calculations."""

import uuid
from datetime import datetime
from typing import Any, Literal
from uuid import UUI<PERSON>

from lc_config.settings import shared_settings as settings
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

from app import schemas
from app.api.auth import AuthInfo, OrgAuthInfo, ShareDatasetInfo, ShareRunInfo
from app.memoize import redis_cache


async def fetch_top_k_feedback_key_types_for_prebuilt_dashboard(
    auth: AuthInfo,
    start_time: datetime,
    end_time: datetime | None,
    session_id: uuid.UUID,
) -> list[dict[str, Literal["numerical", "categorical"]]]:
    """
    Return {feedback_key: "numerical" | "categorical"} for the five most‑common
    keys in the time window.  A key is categorical if *any* row for that key
    has value ∈ {NULL, '', '{}'}.
    """
    query = """
WITH top_keys AS (
    SELECT arrayJoin(topK(5)(key)) AS key
    FROM   feedbacks_rmt FINAL
    WHERE  tenant_id = {tenant_id}
    AND  session_id = {session_id}
    AND  run_id != '00000000-0000-0000-0000-000000000000'
    AND  start_time >= {start_time}
    AND ( {end_time} IS NULL
            OR start_time <= {end_time} )
    AND  comparative_experiment_id IS NULL
)
SELECT
    key,
    IF(value IS NULL OR value = '' OR value = {curly_brace_literal},
    'numerical',
    'categorical') AS feedback_type
FROM   feedbacks_rmt FINAL
WHERE  key IN (SELECT key FROM top_keys)
AND  tenant_id = {tenant_id}
AND  session_id = {session_id}
AND  run_id != '00000000-0000-0000-0000-000000000000'
AND  start_time >= {start_time}
AND ( {end_time} IS NULL
        OR start_time <= {end_time} )
LIMIT 1 by key
"""

    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        rows = await ch.fetch(
            "fetch_top_k_feedback_key_types_for_prebuilt_dashboard",
            query,
            params={
                "tenant_id": str(auth.tenant_id),
                "session_id": str(session_id),
                "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S")
                if end_time
                else None,
                "curly_brace_literal": "{}",
            },
        )

    return [{"key": row["key"], "feedback_type": row["feedback_type"]} for row in rows]


def normalize_stats_query_params(
    params: schemas.RunStatsQueryParams,
) -> schemas.RunStatsQueryParams:
    """Normalize query parameters to improve cache hit rates."""
    params_copy = params.model_copy()
    # Round times to nearest minute
    if params_copy.start_time:
        params_copy.start_time = params_copy.start_time.replace(second=0, microsecond=0)
    if params_copy.end_time:
        params_copy.end_time = params_copy.end_time.replace(second=0, microsecond=0)

    # Sort any lists to ensure consistent ordering
    if params_copy.id:
        if isinstance(params_copy.id, list):
            params_copy.id.sort()

    if params_copy.session:
        if isinstance(params_copy.session, list):
            params_copy.session.sort()

    if params_copy.reference_example:
        if isinstance(params_copy.reference_example, list):
            params_copy.reference_example.sort()

    return params_copy


@redis_cache(ttl=60, exclude_kwargs=["auth"])  # 1-minute TTL
async def cached_fetch_top_k_feedback_key_types(
    _tenant_id: UUID,
    start_time: datetime,
    end_time: datetime | None,
    session_id: uuid.UUID,
    *,
    auth: AuthInfo | OrgAuthInfo,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
) -> list[dict[str, Any]]:
    return await fetch_top_k_feedback_key_types_for_prebuilt_dashboard(
        auth, start_time, end_time, session_id
    )


@redis_cache(ttl=None, exclude_kwargs=["auth"])
async def cached_run_stats(
    _tenant_id: UUID,
    stats_query_params: schemas.RunStatsQueryParams,
    select: list[schemas.RunStatsSelect],
    bucket_info: schemas.CustomChartsSectionRequest,
    max_values: int = 5,
    *,
    auth: AuthInfo | OrgAuthInfo | ShareRunInfo | ShareDatasetInfo,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
) -> dict[str, Any] | dict[datetime, Any]:
    from app.models.runs.stats import run_stats  # Circular import

    return await run_stats(
        auth, stats_query_params, select, bucket_info, max_values, _tenant_id
    )
