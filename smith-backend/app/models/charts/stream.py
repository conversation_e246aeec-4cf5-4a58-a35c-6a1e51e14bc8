import asyncio
import copy
import math
import uuid
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, Async<PERSON><PERSON><PERSON>, Optional, cast

import structlog
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database.database import asyncpg_pool

from app import schemas
from app.api.auth.schemas import AuthInfo, BaseAuthInfo, OrgAuthInfo
from app.models.charts.cached_fetch import (
    cached_fetch_top_k_feedback_key_types,
    fetch_top_k_feedback_key_types_for_prebuilt_dashboard,
)
from app.models.charts.prebuilt import get_tracing_project_prebuilt_dashboard
from app.models.charts.utils import get_prefetch_enabled_start_end_time

logger = structlog.get_logger(__name__)


async def stream_prebuilt_dashboard_for_session(
    session_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
    is_prefetch: bool = False,
) -> AsyncIterator[list[dict]]:
    async with asyncpg_pool() as db:
        session_name = await db.fetchrow(
            """
            SELECT name from tracer_session
            WHERE id=$1
            AND tenant_id=$2
            AND reference_dataset_id IS NULL
            """,
            session_id,
            auth.tenant_id,
        )
        if session_name is None:
            raise HTTPException(
                status_code=404,
                detail="Project doesn't exist or is an experiment.",
            )
        session_name = session_name["name"]

    request.start_time = cast(datetime, request.start_time).replace(microsecond=0)
    request.end_time = (request.end_time or datetime.now(timezone.utc)).replace(
        microsecond=0
    )

    use_prefetched = _use_prefetched(auth, session_id, request)
    # Overwrite global group by max groups
    if (
        use_prefetched
        and request.group_by
        and "max_groups"
        # we know tenant_settings must exist if use_prefetched is true
        in (
            tenant_settings := settings.PREFETCH_PREBUILT_DASHBOARDS_TENANT_CONFIGS[
                str(auth.tenant_id)
            ]
        )
    ):
        request.group_by.max_groups = tenant_settings["max_groups"]

    sub_sections = await _prebuilt_sub_sections(
        auth,
        request,
        session_id,
        use_cache=True,
        cache_ttl=cache_ttl,
        is_prefetch=is_prefetch,
        use_prefetched=use_prefetched,
    )
    dashboard_definition = {
        "title": session_name,
        "description": f"Prebuilt dashboard for tracing project: {session_name}",
        "session_id": session_id,
        "id": session_id,
        "charts": [],
        "sub_sections": sub_sections,
    }

    async for op in _stream_dashboard(
        request,
        auth,
        dashboard_definition,
        cache_ttl,
        use_prefetched=use_prefetched,
        is_prefetch=is_prefetch,
    ):
        yield op


async def _stream_dashboard(
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
    dashboard_definition: dict,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
    use_prefetched: bool = False,
    is_prefetch: bool = False,
) -> AsyncIterator[list[dict]]:
    from app.models.charts.fetch import combine_series_filters

    sub_sections = dashboard_definition.get("sub_sections", [])
    charts: list = dashboard_definition.get("charts", []) or [
        c for s in sub_sections for c in s["charts"]
    ]

    chart_id_to_chart = {chart["id"]: chart for chart in charts}
    chart_id_to_path = {}
    for i, sub_section in enumerate(sub_sections):
        for j, chart in enumerate(sub_section["charts"]):
            chart_id_to_path[chart["id"]] = f"/sub_sections/{i}/charts/{j}"
    for i, chart in enumerate(charts):
        chart["data"] = []
        if chart["id"] not in chart_id_to_path:
            chart_id_to_path[chart["id"]] = f"/charts/{i}"

    all_series_obj: list[schemas.CustomChartSeries] = []
    series_id_to_chart_id = {}
    expected_queries_per_chart = defaultdict(list)
    for chart in charts:
        static_series = []
        for s in chart["series"]:
            if s.get("group_by"):
                s["group_by"]["set_by"] = "series"
            elif request.group_by:
                s["group_by"] = {**request.group_by.model_dump(), "set_by": "section"}
            else:
                pass
            s["filters"] = combine_series_filters(
                s.get("filters"), chart.get("common_filters")
            )
            all_series_obj.append(schemas.CustomChartSeries(**s))
            series_id_to_chart_id[s["id"]] = chart["id"]
            expected_queries_per_chart[chart["id"]].append(s["id"])
            # If group by is applied or the metric is feedback values,
            # we'll dynamically generate the series based on the returned
            # groups/categories.
            if not s.get("group_by") and not s.get("metric") == "feedback_values":
                static_series.append(s)
        chart["series"] = static_series

    # Yield dashboard skeleton
    yield [
        {
            "op": "add",
            "path": "",
            "value": {
                **dashboard_definition,
                **{
                    "sub_sections": copy.deepcopy(sub_sections),
                    "charts": copy.deepcopy(charts if not sub_sections else []),
                },
            },
        }
    ]

    series_id_to_series_obj = {s.id: s for s in all_series_obj}
    series_order = [s.id for s in all_series_obj]
    completed_series_queries = set()
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    async for series_data in stream_chart_data_for_series(
        auth,
        all_series_obj,
        request,
        [],
        schemas.AccessScope.workspace,
        use_cache=settings.USE_CACHE_FOR_DASHBOARD_STREAMING,
        cache_ttl=cache_ttl,
        use_prefetched=use_prefetched,
        is_prefetch=is_prefetch,
    ):
        # All the datapoints have the same series_id

        # skip if no datapoints for this series
        if len(series_data) == 0:
            continue

        series_id = series_data[0].series_id
        completed_series_queries.add(series_id)
        chart_id = series_id_to_chart_id[series_id]
        chart = chart_id_to_chart[chart_id]
        chart_path = chart_id_to_path[chart_id]

        series_data = _unpack_feedback_datapoints(
            series_data,
            chart_id_to_chart,
            series_id_to_chart_id,
            series_id_to_series_obj,
            categorical_feedback_keys_to_categories,
        )

        # If group_by: dynamically add the series to the chart
        group_to_series_id: dict = {}
        for sid, group in set(
            (dp.series_id, dp.group)
            for dp in series_data
            if dp.group is not None and dp.group != ""
        ):
            new_sid: Any = sid + ":" + group
            orig_series_obj = series_id_to_series_obj[sid]
            group_to_series_id[(sid, group)] = new_sid
            if new_sid not in [s["id"] for s in chart["series"]]:
                group_series = orig_series_obj.model_dump()
                group_series["id"] = new_sid
                group_series["filters"] = combine_series_filters(
                    group_series["filters"],
                    _group_by_to_filters(
                        cast(schemas.RunStatsGroupBy, orig_series_obj.group_by),
                        group,
                    ),
                )
                chart["series"].append(group_series)
        chart["series"] = sorted(
            chart["series"],
            key=lambda s: (
                series_order.index(s["id"].split(":")[0]),
                *s["id"].split(":")[1:],
            ),
        )

        for datapoint in series_data:
            if new_sid := group_to_series_id.get(
                (datapoint.series_id, datapoint.group)
            ):
                datapoint.series_id = new_sid

        chart["data"].extend(series_data)

        # Only yield chart data once we've collected all series in the chart
        if all(
            sid in completed_series_queries
            for sid in expected_queries_per_chart[chart["id"]]
        ):
            ops = []
            # Update series if there are any dynamic series.
            if any(":" in s["id"] for s in chart["series"]):
                ops.append(
                    {
                        "op": "replace",
                        "path": f"{chart_path}/series",
                        "value": chart["series"],
                    }
                )
            chart["data"].sort(
                key=lambda dp: (
                    dp.timestamp,
                    series_order.index(dp.series_id.split(":")[0]),
                    *(dp.series_id.split(":")[1:]),
                )
            )
            ops.append(
                {"op": "replace", "path": f"{chart_path}/data", "value": chart["data"]}
            )
            yield ops


async def _stream_chart_data_for_series_segment(
    auth: AuthInfo | OrgAuthInfo,
    series: list[schemas.CustomChartSeries],
    request: schemas.CustomChartsSectionRequest,
    common_filters: list[schemas.CustomChartSeriesFilters | None],
    segments: list[tuple[datetime, datetime]],
    groups: dict[str, list[str | None]],
    use_cache: bool = False,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
    is_prefetch: bool = False,
    use_prefetched: bool = False,
) -> AsyncIterator[tuple[dict[str, list[str]], list[schemas.CustomChartsDataPoint]]]:
    # Create a semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(settings.CHARTS_STATS_SEMAPHORE)

    # Process each series one at a time to stream results
    common_filters_ = common_filters or (None for _ in series)

    tasks = []
    for s, cf in zip(series, common_filters_):
        for st, et in segments:
            tasks.append(
                asyncio.create_task(
                    _fetch_stats_include_error(
                        s,
                        cf,
                        request,
                        auth,
                        use_cache,
                        semaphore,
                        schemas.RunsFilterDataSourceTypeEnum.historical,
                        st,
                        et,
                        groups.get(str(s.id)),
                        cache_ttl,
                    )
                )
            )
    # If we're using prefetched data, we can wait to gather all the segments since they should be cache hits.
    # This allows us to correctly unpack categorical feedback.
    if not is_prefetch and use_prefetched:
        results_by_sid: dict = {}
        task_results = await asyncio.gather(*tasks)
        for s, result in task_results:
            if isinstance(result, Exception):
                # Log error but continue processing other series
                logger.error(
                    "Error fetching stats for series",
                    series=s.model_dump(),
                    exc_info=result,
                )
                continue
            sid = str(s.id)
            results_by_sid[sid] = results_by_sid.get(sid) or {}
            if s.group_by:
                for g, bucket in result.items():
                    results_by_sid[sid][g] = results_by_sid[sid].get(g) or {}
                    for ts, stats in bucket.items():
                        results_by_sid[sid][g][ts] = stats
            else:
                for ts, stats in result.items():
                    results_by_sid[sid][ts] = stats
        for sid, result in results_by_sid.items():
            s = [s for s, _ in task_results if str(s.id) == sid][0]
            yield _stats_results_to_datapoints(s, result, groups)  # type: ignore[arg-type]
    else:
        for task in asyncio.as_completed(tasks):
            s, result = await task
            if isinstance(result, Exception):
                # Log error but continue processing other series
                logger.error(
                    "Error fetching stats for series",
                    series=s.model_dump(),
                    exc_info=result,
                )
                continue
            yield _stats_results_to_datapoints(s, result, groups)


async def stream_chart_data_for_series(
    auth: AuthInfo | OrgAuthInfo,
    series: list[schemas.CustomChartSeries],
    request: schemas.CustomChartsSectionRequest,
    common_filters: list[schemas.CustomChartSeriesFilters | None],
    access_scope: schemas.AccessScope,
    use_cache: bool = False,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
    use_prefetched: bool = False,
    is_prefetch: bool = False,
) -> AsyncIterator[list[schemas.CustomChartsDataPoint]]:
    """Yield datapoints for a series as they become available."""

    # chop the microseconds to avoid an extra bucket at the end
    if request.start_time is None:
        raise HTTPException(
            status_code=404,
            detail="start_time must be set.",
        )
    stride = request.stride
    total_seconds_stride = timedelta(
        days=stride.days,
        hours=stride.hours,
        minutes=stride.minutes,
    ).total_seconds()
    start_time = request.start_time
    end_time = cast(datetime, request.end_time)

    # Split the time range into segments of n_buckets_per_fetch buckets
    segments = []
    # When prefetching is enabled we stream one bucket at a time, since all but the last should be cache hits.
    if use_prefetched:
        start_time, end_time = _request_to_prefetch_start_end_time(
            request, is_prefetch=is_prefetch
        )
        total_seconds_window = (end_time - start_time).total_seconds()
        num_buckets = math.ceil(total_seconds_window / total_seconds_stride)
        for i in range(num_buckets):
            st = start_time + timedelta(seconds=i * total_seconds_stride)
            et = start_time + timedelta(seconds=(i + 1) * total_seconds_stride)
            segments.append((st, min(et, end_time)))
        segments = list(reversed(segments))
    elif (
        isinstance(auth, BaseAuthInfo)
        and (
            str(auth.tenant_id) in settings.BUCKET_STREAMING_TENANTS
            or "*" in settings.BUCKET_STREAMING_TENANTS
        )
        and end_time - start_time
        > timedelta(hours=settings.BUCKET_STREAMING_WINDOW_THRESHOLD_HOURS)
    ):
        et = end_time
        st = et - timedelta(
            seconds=total_seconds_stride * settings.CHARTS_STREAMED_BUCKETS_PER_FETCH
        )
        segments.append((max(st, start_time), et))
        while st > start_time:
            et = st
            st = et - timedelta(
                seconds=total_seconds_stride
                * settings.CHARTS_STREAMED_BUCKETS_PER_FETCH
            )
            segments.append(
                (max(st, start_time), max(et - timedelta(seconds=1), start_time))
            )
    else:
        segments.append((start_time, end_time))

    enforce_same_groups = not use_prefetched
    series_groups: dict[str, list[Optional[str]]] = defaultdict(list)
    if enforce_same_groups:
        latest_segment = segments[0]
        segments = segments[1:]
        async for sg, series_data in _stream_chart_data_for_series_segment(
            auth,
            series,
            request,
            common_filters,
            [latest_segment],
            series_groups,
            use_cache,
            cache_ttl,
            is_prefetch=is_prefetch,
            use_prefetched=use_prefetched,
        ):
            for sid, g in sg.items():
                series_groups[sid].extend(g)
            yield series_data
        series_groups = {
            sid: list(set(groups)) for sid, groups in series_groups.items()
        }

    # Rest of the segments. Here if its a group by series and there is more
    # than one segment, we will use the groups from the first segment to fetch
    # the stats for the rest of the segments.
    if segments:
        async for _, series_data in _stream_chart_data_for_series_segment(
            auth,
            series,
            request,
            common_filters,
            segments,
            series_groups,
            use_cache,
            cache_ttl,
            is_prefetch=is_prefetch,
            use_prefetched=use_prefetched,
        ):
            yield series_data


async def _fetch_stats_include_error(
    s: schemas.CustomChartSeries, *args: Any, **kwargs: Any
) -> tuple[schemas.CustomChartSeries, dict | Exception]:
    from app.models.charts.fetch import fetch_stats

    try:
        return s, (await fetch_stats(s, *args, **kwargs))
    except Exception as e:
        return s, e


async def _prebuilt_sub_sections(
    auth: AuthInfo | OrgAuthInfo,
    request: schemas.CustomChartsRequestBase,
    session_id: uuid.UUID,
    use_cache: bool = False,
    cache_ttl: int = settings.PREBUILT_DASHBOARD_CACHE_TTL_SEC,
    is_prefetch: bool = False,
    use_prefetched: bool = False,
) -> list[dict]:
    # Fetch the top k feedback keys
    if request.start_time is None:
        raise HTTPException(
            status_code=404,
            detail="start_time must be set.",
        )
    if use_cache:
        if use_prefetched:
            rounded_start, rounded_end = _request_to_prefetch_start_end_time(
                request, is_prefetch=is_prefetch
            )
        else:
            rounded_start = request.start_time.replace(second=0, microsecond=0)
            rounded_end = None
            if request.end_time:
                rounded_end = request.end_time.replace(second=0, microsecond=0)

        feedback_key_types = await cached_fetch_top_k_feedback_key_types(
            auth.tenant_id,
            rounded_start,
            rounded_end,
            session_id,
            auth=auth,
            cache_ttl=cache_ttl,
        )
    else:
        feedback_key_types = (
            await fetch_top_k_feedback_key_types_for_prebuilt_dashboard(
                auth, request.start_time, request.end_time, session_id
            )
        )

    # Add a chart and series for each feedback key
    feedback_charts = []
    for i, feedback_key_type in enumerate(feedback_key_types):
        feedback_key = feedback_key_type["key"]
        feedback_type = feedback_key_type["feedback_type"]
        metric = (
            "feedback_score_avg" if feedback_type == "numerical" else "feedback_values"
        )
        description = (
            f"{feedback_key} counts over time"
            if feedback_type == "categorical"
            else f"Average {feedback_key} score over time"
        )

        chart = {
            "id": f"chart-{feedback_key}",
            "title": f"{feedback_key}",
            "description": description,
            "chart_type": "line",
            "index": i,
            "series": [
                {
                    "id": f"series-{feedback_key}",
                    "name": "Avg",
                    "metric": metric,
                    "feedback_key": feedback_key,
                }
            ],
        }
        feedback_charts.append(chart)

    prebuilt_dashboard = get_tracing_project_prebuilt_dashboard()
    sub_sections: list[dict] = prebuilt_dashboard["sub_sections"] + [
        {
            "title": "Feedback Scores",
            "description": "Feedback scores over time",
            "index": len(prebuilt_dashboard["sub_sections"]),
            "id": "feedback",
            "charts": feedback_charts,
        }
    ]
    for sub_section in sub_sections:
        for chart in sub_section["charts"]:
            chart["common_filters"] = {"session": [session_id]}
    return sub_sections


def _unpack_feedback_datapoints(
    all_series_data: list[schemas.CustomChartsDataPoint],
    chart_id_to_chart: dict,
    series_id_to_chart_id: dict,
    series_id_to_series_obj: dict,
    categorical_feedback_keys_to_categories: dict[str, set[str]],
) -> list[schemas.CustomChartsDataPoint]:
    from app.models.charts.fetch import combine_series_filters

    for datapoint in all_series_data:
        chart = chart_id_to_chart[series_id_to_chart_id[datapoint.series_id]]
        if (
            isinstance(datapoint.value, dict)
            and (categories := list(list(datapoint.value.values())[0]["values"].keys()))
            and series_id_to_series_obj[datapoint.series_id].metric == "feedback_values"
        ):
            # should only be one top level key which represents the feedback key
            feedback_key = list(datapoint.value.keys())[0]
            categorical_feedback_keys_to_categories[feedback_key].update(categories)
            for category in categorical_feedback_keys_to_categories[feedback_key]:
                new_sid = datapoint.series_id + ":" + category
                chart["series"] = [
                    s for s in chart["series"] if s["id"] != datapoint.series_id
                ]
                if new_sid not in [s["id"] for s in chart["series"]]:
                    orig_series = series_id_to_series_obj[datapoint.series_id]
                    category_series = orig_series.model_dump()
                    category_series["name"] = category
                    category_series["id"] = new_sid
                    category_series["metric"] = "run_count"
                    category_series.pop("feedback_key")
                    category_series["filters"] = combine_series_filters(
                        category_series["filters"],
                        {
                            "filter": f'and(eq(feedback_key, "{feedback_key}"), eq(feedback_value, "{category}"))'
                        },
                    )
                    if not category_series.get("group_by"):
                        chart["series"].append(category_series)
                    series_id_to_series_obj[new_sid] = schemas.CustomChartSeries(
                        **category_series
                    )
    updated_datapoints = []
    for datapoint in all_series_data:
        chart = chart_id_to_chart[series_id_to_chart_id[datapoint.series_id]]
        if isinstance(datapoint.value, dict):
            # should only be one top level key which represents the feedback key
            feedback_key = list(datapoint.value.keys())[0]
            if category_set := categorical_feedback_keys_to_categories.get(
                feedback_key
            ):
                counts = {
                    **{c: 0 for c in category_set},
                    **datapoint.value[feedback_key]["values"],
                }
                for category, count in counts.items():
                    new_sid = datapoint.series_id + ":" + category
                    series_id_to_chart_id[new_sid] = chart["id"]
                    new_datapoint = datapoint.model_copy(
                        update={"value": count, "series_id": new_sid}
                    )
                    updated_datapoints.append(new_datapoint)
            else:
                datapoint.value = datapoint.value[feedback_key]["avg"]
                updated_datapoints.append(datapoint)
        else:
            updated_datapoints.append(datapoint)
    return updated_datapoints


def _group_by_to_filters(
    group_by: schemas.RunStatsGroupBy, group: str
) -> dict[str, str]:
    new_filters = {}
    if group_by.attribute == "name":
        new_filters["filter"] = f'eq(name, "{group}")'
    elif group_by.attribute == "run_type":
        new_filters["filter"] = f'eq(run_type, "{group}")'
    elif group_by.attribute == "tag":
        new_filters["filter"] = f'eq(tag, "{group}")'
    elif group_by.attribute == "metadata":
        new_filters["filter"] = (
            f'and(eq(metadata_key, "{group_by.path}"), eq(metadata_value, "{group}"))'
        )
    else:
        raise ValueError(...)
    return new_filters


def _use_prefetched(
    auth: BaseAuthInfo | OrgAuthInfo,
    session_id: str | uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
) -> bool:
    from app.models.charts.jobs import (
        PREFETCH_INTERVAL_DAYS,
        WINDOW_DAYS_TO_STRIDE_MINUTES,
    )

    prefetch_sessions = settings.PREFETCH_PREBUILT_DASHBOARDS_TENANT_CONFIGS.get(
        str(auth.tenant_id), {}
    ).get("session_ids", [])
    prefetch_enabled = str(session_id) in prefetch_sessions
    time_window_days = (request.end_time - request.start_time).days  # type: ignore[operator, operator]
    stride_minutes = request.stride.hours * 60 + request.stride.minutes
    has_prefetched = (
        time_window_days in PREFETCH_INTERVAL_DAYS
        and WINDOW_DAYS_TO_STRIDE_MINUTES.get(time_window_days) == stride_minutes
    )
    return prefetch_enabled and has_prefetched


def _request_to_prefetch_start_end_time(
    request: schemas.CustomChartPreviewRequest, is_prefetch: bool = False
) -> tuple[datetime, datetime]:
    stride = request.stride
    total_seconds_stride = timedelta(
        days=stride.days,
        hours=stride.hours,
        minutes=stride.minutes,
    ).total_seconds()
    time_window_days = (request.end_time - request.start_time).days
    stride_hrs = int(total_seconds_stride // 3600)
    return get_prefetch_enabled_start_end_time(
        request.end_time,
        time_window_days,
        stride_hrs,
        is_prefetch=is_prefetch,
    )


def _stats_results_to_datapoints(
    s: schemas.CustomChartSeries, result: dict, groups: dict
) -> tuple[dict, list]:
    from app.models.charts.fetch import chart_metric_to_stats_select

    sid = str(s.id)
    series_data = []
    if not s.group_by:
        result = {None: result}
    all_groups = defaultdict(list)
    for group, bucket in result.items():
        raw_group = group
        if group:
            group = group.strip('"').strip("'")

        for ts, stats in bucket.items():
            select = chart_metric_to_stats_select(s.metric).value
            val = stats[select]
            if select == schemas.RunStatsSelect.feedback_stats:
                feedback_val = val.get(s.feedback_key) or {
                    "n": 0,
                    "avg": None,
                    "values": {},
                }
                val = {s.feedback_key: feedback_val}

            # handle the case where none of the groups are present for bucket
            if group == "" and len(groups[sid]) > 0:
                for grp in groups[sid]:
                    dp = schemas.CustomChartsDataPoint(
                        series_id=sid,
                        timestamp=ts,
                        value=val,
                        group=grp.strip('"').strip("'") if grp else None,
                    )
                    series_data.append(dp)
            else:
                dp = schemas.CustomChartsDataPoint(
                    series_id=sid,
                    timestamp=ts,
                    value=val,
                    group=group,
                )
                # only cache non-empty groups
                if group:
                    all_groups[sid].append(raw_group)
                # if there is no group_by or the group is not empty, add the datapoint to the series
                if not s.group_by or group:
                    series_data.append(dp)

    return all_groups, series_data
