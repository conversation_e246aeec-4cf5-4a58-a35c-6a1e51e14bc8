from datetime import datetime, timedelta

from lc_config.settings import shared_settings as settings


def get_prefetch_enabled_start_end_time(
    current_time: datetime,
    time_window_days: int,
    stride_hrs: int,
    is_prefetch: bool = False,
) -> tuple[datetime, datetime]:
    # discretize by refresh rate. e.g., if refresh is every 60 minutes, round down to nearest hour.
    end_time = get_prefetch_end_time(
        current_time, settings.PREFETCH_PREBUILT_DASHBOARDS_REFRESH_RATE_MINUTES
    )
    # discretize by stride. e.g., if stride is 8 hrs, round down to 12am, 8am, or 4pm.
    start_hr = _floor_to_nearest_multiple(current_time.hour, stride_hrs)
    start_time = (end_time - timedelta(days=time_window_days)).replace(
        hour=start_hr, minute=0
    )
    # on user-generated fetches, subtract one refresh period to ensure we get a cache hit
    if not is_prefetch:
        end_time = end_time - timedelta(
            minutes=settings.PREFETCH_PREBUILT_DASHBOARDS_REFRESH_RATE_MINUTES
        )
    return (start_time, end_time)


def _floor_to_nearest_multiple(value: int, multiple: int) -> int:
    """Round down a value to the nearest multiple of another value."""
    return value - (value % multiple)


def get_prefetch_end_time(
    current_time: datetime, refresh_rate_minutes: int
) -> datetime:
    """
    Rounds down the current time to the nearest multiple of refresh_rate_minutes.
    For example, if refresh_rate_minutes is 15 and the time is 10:37, this returns 10:30.
    """
    if refresh_rate_minutes < 1:
        raise ValueError("Refresh rate must be at least 1 minute.")
    elif refresh_rate_minutes > 24 * 60:
        raise ValueError("Refresh rates must be less than 24 hours.")

    minutes_into_day = current_time.hour * 60 + current_time.minute
    floored_minutes_into_day = _floor_to_nearest_multiple(
        minutes_into_day, refresh_rate_minutes
    )
    end_hr = int(floored_minutes_into_day // 60)
    end_minute = int(floored_minutes_into_day % 60)
    return current_time.replace(hour=end_hr, minute=end_minute, second=0, microsecond=0)
