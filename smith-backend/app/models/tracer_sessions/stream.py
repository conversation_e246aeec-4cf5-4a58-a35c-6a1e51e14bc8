import logging
from datetime import datetime, timedelta, timezone
from typing import AsyncIterator
from uuid import UUID

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from lc_config.settings import shared_settings as settings
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_pool

from app.api.auth.schemas import AuthInfo, ShareDatasetInfo
from app.models.query_lang.parse import (
    Operation,
    Operator,
    parse_and_split_filters_tracer_sessions,
)
from app.models.query_lang.translate import (
    SqlVisitorPostgres,
    translate_session_feedback_operation_to_ch_query,
)
from app.models.tracer_sessions.attrs import get_tracer_session_attributes
from app.models.tracer_sessions.expressions import (
    sql_stats_for_session,
    sql_stats_for_session_agg,
    sql_stats_w_facets_for_session,
    sql_stats_w_facets_for_session_agg,
    sql_stats_w_facets_for_session_without_sessions_agg,
)
from app.models.tracer_sessions.sort_ch import get_sorted_session_ids_from_ch
from app.models.tracer_sessions.sql import (
    get_list_sessions_sql,
    get_session_sql_wout_stats,
)
from app.models.tracer_sessions.stats import (
    map_stats,
)
from app.retry import retry_asyncpg, retry_clickhouse_read
from app.schemas import (
    SORTABLE_COLUMNS_IN_POSTGRES,
    FilterQueryParamsForTracerSessionSchema,
    QueryParamsForSingleTracerSessionSchema,
    SessionSortableColumns,
)

logger = logging.getLogger(__name__)


def _default_stats_start_time() -> str:
    dt = datetime.now(timezone.utc) - timedelta(days=7)
    return dt.strftime("%Y-%m-%d %H:%M:%S.%f")


@retry_asyncpg
async def _fetch_session(
    auth: AuthInfo,
    tracer_session_id: UUID,
) -> dict:
    async with asyncpg_pool() as db:
        row = await db.fetchrow(
            get_session_sql_wout_stats,
            tracer_session_id,
            auth.tenant_id,
        )
    assert auth.exists(row)
    return row


@retry_clickhouse_read
async def _fetch_stats(auth: AuthInfo, tracer_session_id: UUID, row: dict) -> dict:
    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        name = "fetch_stats_w_facets_for_session"
        can_use_topk_agg_merge_tree = (
            settings.FF_USE_TOPK_AGGREGATED_MERGE_TREE
            or str(auth.tenant_id)
            in settings.TOPK_AGGREGATED_MERGE_TREE_PROD_TENANT_IDS
        )
        can_use_session_stats_agg_merge_tree = (
            settings.STATS_USE_SESSION_AGGREGATION
            or str(auth.tenant_id) in settings.STATS_AGGREGATION_TENANT_IDS
        )
        if can_use_topk_agg_merge_tree and can_use_session_stats_agg_merge_tree:
            query = sql_stats_w_facets_for_session_agg
        elif can_use_topk_agg_merge_tree:
            query = sql_stats_w_facets_for_session_without_sessions_agg
        else:
            query = sql_stats_w_facets_for_session
        stats = await ch.fetchrow(
            name,
            query,
            params={
                "tenant_id": auth.tenant_id,
                "session_ids": [tracer_session_id],
                "min_start_time": row["start_time"] - timedelta(hours=1)
                if row["reference_dataset_id"]
                else _default_stats_start_time(),
            },
        )
    return stats


async def stream_session(
    auth: AuthInfo,
    tracer_session_id: UUID,
    query_params: QueryParamsForSingleTracerSessionSchema,
) -> AsyncIterator[list[dict]]:
    """
    Stream information about a tracer session.
    """
    row = await _fetch_session(auth, tracer_session_id)
    yield [
        {
            "op": "add",
            "path": "",
            "value": {**row},
        }
    ]
    if query_params.include_stats:
        stats = await _fetch_stats(auth, tracer_session_id, row)
        yield [
            {"op": "add", "path": f"/{key}", "value": value}
            for key, value in map_stats(stats, True).items()
        ]


@retry_asyncpg
async def _fetch_sessions(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: FilterQueryParamsForTracerSessionSchema,
    pg_session_clause: str | None = None,
    pg_session_params: dict | None = None,
) -> list[dict]:
    """
    Fetch sessions from the database.
    """
    list_sql = get_list_sessions_sql(
        auth, query_params, pg_session_clause, pg_session_params
    )

    async with asyncpg_pool() as pool:
        return await pool.fetch(list_sql.sql, *list_sql.args)


@retry_clickhouse_read
async def _fetch_stats_by_id(
    auth: AuthInfo,
    query_params: FilterQueryParamsForTracerSessionSchema,
    rows: list[dict],
) -> dict[UUID, dict]:
    """
    Fetch stats for sessions from the database.
    """
    all_time_stats = query_params.reference_dataset or any(
        row["reference_dataset_id"] for row in rows
    )

    if query_params.facets:
        name = "fetch_stats_w_facets_for_session"
        can_use_topk_agg_merge_tree = (
            settings.FF_USE_TOPK_AGGREGATED_MERGE_TREE
            or str(auth.tenant_id)
            in settings.TOPK_AGGREGATED_MERGE_TREE_PROD_TENANT_IDS
        )
        can_use_session_stats_agg_merge_tree = (
            settings.STATS_USE_SESSION_AGGREGATION
            or str(auth.tenant_id) in settings.STATS_AGGREGATION_TENANT_IDS
        )
        if can_use_topk_agg_merge_tree and can_use_session_stats_agg_merge_tree:
            query = sql_stats_w_facets_for_session_agg
        elif can_use_topk_agg_merge_tree:
            query = sql_stats_w_facets_for_session_without_sessions_agg
        else:
            query = sql_stats_w_facets_for_session
    else:
        name = "fetch_stats_for_session"
        query = (
            sql_stats_for_session_agg
            if (
                settings.STATS_USE_SESSION_AGGREGATION
                or str(auth.tenant_id) in settings.STATS_AGGREGATION_TENANT_IDS
                or (settings.FF_USE_APPROX_STATS and query_params.use_approx_stats)
            )
            else sql_stats_for_session
        )

    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        stats = await ch.fetch(
            name,
            query,
            params={
                "tenant_id": auth.tenant_id,
                "session_ids": [UUID(row["id"].hex) for row in rows],
                "min_start_time": min(row["start_time"] for row in rows)
                - timedelta(hours=1)
                if all_time_stats
                else _default_stats_start_time(),
            },
        )

    return {stat["session_id"]: stat for stat in stats}


async def stream_sessions(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: FilterQueryParamsForTracerSessionSchema,
) -> AsyncIterator[list[dict]]:
    """Get all tracer sessions that belong to the owner."""
    original_offset = query_params.offset
    if isinstance(auth, ShareDatasetInfo):
        query_params = FilterQueryParamsForTracerSessionSchema(
            **{**query_params.model_dump(), "reference_dataset": [auth.dataset_id]}
        )

    pg_session_clause, pg_session_params = None, None
    ch_session_clause, ch_session_params = None, None
    if query_params.filter:
        pg_directive, ch_directive = parse_and_split_filters_tracer_sessions(
            query_params.filter
        )
        if pg_directive:
            filter_directive = Operation(operator=Operator.AND, arguments=[])
            filter_directive.arguments.append(pg_directive)
            try:
                (pg_session_clause, pg_session_params, _, _, _) = (
                    filter_directive.accept(
                        SqlVisitorPostgres(
                            attributes=get_tracer_session_attributes("tracer_session"),
                            main_table="tracer_session",
                        )
                    )
                )
            except ValueError as e:
                raise HTTPException(status_code=400, detail=str(e)) from e
        if ch_directive:
            try:
                (
                    ch_session_clause,
                    ch_session_params,
                ) = translate_session_feedback_operation_to_ch_query(
                    ch_directive, auth.tenant_id
                )
            except ValueError as e:
                raise HTTPException(status_code=400, detail=str(e)) from e

    sort_from_ch = query_params.sort_by not in SORTABLE_COLUMNS_IN_POSTGRES

    if sort_from_ch:
        session_ids = await get_sorted_session_ids_from_ch(
            auth, query_params, ch_session_clause, ch_session_params
        )

        if not session_ids:
            yield [
                {
                    "op": "add",
                    "path": "",
                    "value": {
                        "rows": [],
                        "total": 0,
                        "excluding_empty": sort_from_ch,
                    },
                }
            ]
            return

        # No need to pass offset here since it is handled by the clickhouse query
        query_params = FilterQueryParamsForTracerSessionSchema(
            id=session_ids,
            limit=query_params.limit,
            sort_by=SessionSortableColumns.START_TIME,
            reference_dataset=query_params.reference_dataset,
            tag_value_id=query_params.tag_value_id,
            dataset_version=query_params.dataset_version,
            name_contains=query_params.name_contains,
            name=query_params.name,
            metadata=query_params.metadata,
            reference_free=query_params.reference_free,
            include_stats=query_params.include_stats,
            use_approx_stats=query_params.use_approx_stats,
        )
    elif ch_session_clause:
        # query clickhouse to get the session ids
        async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
            rows = await ch.fetch(
                "fetch_ch_session_filter_clause",
                ch_session_clause,
                params=ch_session_params,
            )
        session_ids = [row["session_id"] for row in rows]

        if not session_ids:
            yield [
                {
                    "op": "add",
                    "path": "",
                    "value": {
                        "rows": [],
                        "total": 0,
                        "excluding_empty": bool(ch_session_clause),
                    },
                }
            ]
            return

        query_params = FilterQueryParamsForTracerSessionSchema(
            id=session_ids,
            offset=query_params.offset,
            limit=query_params.limit,
            sort_by=SessionSortableColumns.START_TIME,
            reference_dataset=query_params.reference_dataset,
            tag_value_id=query_params.tag_value_id,
            dataset_version=query_params.dataset_version,
            name_contains=query_params.name_contains,
            name=query_params.name,
            metadata=query_params.metadata,
            reference_free=query_params.reference_free,
            include_stats=query_params.include_stats,
            use_approx_stats=query_params.use_approx_stats,
        )

    rows = await _fetch_sessions(
        auth, query_params, pg_session_clause, pg_session_params
    )

    if sort_from_ch:
        rows_to_return = sorted(rows, key=lambda row: session_ids.index(row["id"]))
        rows_to_return = rows_to_return[: query_params.limit]
        total = original_offset + len(session_ids)
    else:
        rows_to_return = rows[: query_params.limit]
        total = original_offset + len(rows)

    yield [
        {
            "op": "add",
            "path": "",
            "value": {
                "rows": [{**row} for row in rows_to_return],
                "total": total,
                "excluding_empty": sort_from_ch,
            },
        }
    ]

    if not rows_to_return:
        # short circuit if there are no rows to return
        return

    if query_params.include_stats:
        stats_by_id = await _fetch_stats_by_id(auth, query_params, rows_to_return)

        yield [
            {
                "op": "add",
                "path": f"/rows/{i}/{k}",
                "value": v,
            }
            for i, row in enumerate(rows_to_return)
            for k, v in map_stats(
                stats_by_id.get(row["id"]), query_params.facets
            ).items()
        ]
