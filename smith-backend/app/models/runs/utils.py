import re
from datetime import datetime, timedelta, timezone
from typing import Any, Li<PERSON>, NamedTuple
from uuid import UUID, uuid4

import orjson
import structlog
from lc_database.queue.serializer import ORJ<PERSON>NSerializer
from pydantic import ValidationError

from app import schemas
from app.models.feedback.ingest import FeedbackInsert


class TimeWindows(NamedTuple):
    raw_start: datetime | None
    raw_end: datetime | None
    amt_start: datetime | None
    amt_end: datetime | None
    trailing_start: datetime | None
    trailing_end: datetime | None
    leading_start: datetime | None
    leading_end: datetime | None


def extract_code_evaluator_feedback_keys(
    python_code: str, evaluator_name: str | None = None
) -> list:
    # Find the start of the perform_eval function
    function_start = python_code.find("def perform_eval")
    if function_start == -1:
        return []

    # Find the next function definition or the end of the string
    next_function_start = python_code.find("def ", function_start + 1)
    function_end = (
        len(python_code) if next_function_start == -1 else next_function_start
    )

    # Extract the function content
    function_content = python_code[function_start:function_end]

    # Remove comments first (both single-line and inline comments)
    without_comments = re.sub(r"#.*$", "", function_content, flags=re.MULTILINE)

    # Check for dict() constructor pattern
    return_dict_match = re.search(r"return\s+dict\s*\(([^)]*)\)", without_comments)
    if return_dict_match:
        dict_args = return_dict_match.group(1)
        # Extract keys from kwargs format: key1=val1, key2=val2
        dict_keys = [
            param.split("=")[0].strip()
            for param in dict_args.split(",")
            if "=" in param
        ]
        if dict_keys:
            return dict_keys

    # Find the last dictionary-like structure
    dict_matches = list(
        re.finditer(r"{\s*(['\"])((?:(?!\1)[^:])*)\1(?:\s*#[^\n]*)?:", without_comments)
    )

    if len(dict_matches) == 0:
        return [evaluator_name] if evaluator_name else []

    last_dict = dict_matches[-1]
    dict_start = last_dict.start()
    dict_text_slice = without_comments[dict_start:]

    # Extract all keys from the last dictionary
    keys = []
    key_regex = r"(['\"])((?:(?!\1)[^:])*)\1(?:\s*#[^\n]*)?:"
    for match in re.finditer(key_regex, without_comments[dict_start:]):
        keys.append(match.group(2))

    # Case 1: {"key": "foo", "score": 1, "comment": "bar"}
    if "key" in keys and ("score" in keys or "value" in keys):
        # Extract the value of the "key" field
        key_value_match = re.search(
            r'["\']key["\']\s*:\s*["\']([^"\']+)["\']', dict_text_slice
        )
        if key_value_match:
            return [key_value_match.group(1)]

    # Case 2: {"score": 1, "comment": "foo"} or {"value": 1, "comment": "foo"}
    if ("score" in keys or "value" in keys) and "key" not in keys:
        return [evaluator_name] if evaluator_name else []

    # Default case: return all keys
    return keys


logger = structlog.getLogger(__name__)


def verify_rule_valid_for_corrections_and_get_feedback_key(
    rule: schemas.RunRulesCreateSchema,
) -> str | None:
    if (
        rule.evaluators is None
        or len(rule.evaluators) != 1
        or rule.evaluators[0].structured.tool_schema is None
    ):
        return None
    tool_keys = [
        key
        for key in list(
            rule.evaluators[0].structured.tool_schema.get("properties", {}).keys()
        )
        if key != "comment"
    ]
    if len(tool_keys) != 1:
        return None
    return tool_keys[0]


def convert_to_string_if_present(value: Any) -> Any:
    if value is None:
        return value
    return str(value)


def process_feedback_value_stats(
    feedback: dict[str, Any],
    max_values: int | None = None,
    bucketed_stats: dict[datetime, Any] | None = None,
) -> list[dict[str, Any]]:
    feedback_values: dict[str, Any] = {}
    total_feedback_values: dict[str, Any] = {}
    for combined_key, value_count in zip(
        feedback.get("feedback_value_keys", []) or [],
        feedback.get("feedback_value_counts", []) or [],
    ):
        key, value = combined_key.split("|~|")
        try:
            value = orjson.loads(value)
        except orjson.JSONDecodeError:
            pass
        if not isinstance(value, str):
            continue
        if key not in feedback_values:
            feedback_values[key] = {}
        if value not in feedback_values[key]:
            feedback_values[key][value] = 0
        feedback_values[key][value] += value_count
    if bucketed_stats is not None and max_values is not None:
        for single_bucket in bucketed_stats.values():
            for combined_key, value_count in zip(
                single_bucket.get("feedback_value_keys", []) or [],
                single_bucket.get("feedback_value_counts", []) or [],
            ):
                key, value = combined_key.split("|~|")
                try:
                    value = orjson.loads(value)
                except orjson.JSONDecodeError:
                    pass
                if not isinstance(value, str):
                    continue
                if key not in total_feedback_values:
                    total_feedback_values[key] = {}
                if value not in total_feedback_values[key]:
                    total_feedback_values[key][value] = 0
                total_feedback_values[key][value] += value_count

    if max_values is not None:
        # prune the feedback values to only include the top max_values
        if bucketed_stats is not None:
            # take the top n across all buckets (total_feedback_values) - we want to take the same values in each bucket, so they should be
            # the same across all buckets
            for key in total_feedback_values:
                total_feedback_values[key] = dict(
                    sorted(
                        total_feedback_values[key].items(),
                        key=lambda x: x[1],
                        reverse=True,
                    )[:max_values]
                )
            for key in feedback_values:
                feedback_values[key] = {
                    k: v
                    for k, v in feedback_values[key].items()
                    if k in total_feedback_values[key]
                }
        else:
            for key in feedback_values:
                feedback_values[key] = dict(
                    sorted(
                        feedback_values[key].items(), key=lambda x: x[1], reverse=True
                    )[:max_values]
                )
    feedback_values_sorted = [
        feedback_values.get(key, {}) for key in feedback.get("feedback_keys", []) or []
    ]
    return feedback_values_sorted


async def parse_feedback(
    obj: Any,
    run: dict[str, Any],
    rule_id: UUID,
    run_id: str,
    feedback_inserts: list[FeedbackInsert],
    evaluator_feedbacks_ids: dict[str, list[UUID]],
    depth=0,
    error: str | None = None,
) -> None:
    """Log a warning if the feedback fails validation."""
    if obj is None or depth > 3:
        return
    comment = None
    if len(obj) in [2, 3] and "comment" in obj:
        comment = obj["comment"]
        del obj["comment"]
    if len(obj) == 2 and "key" in obj and ("score" in obj or "value" in obj):
        key_to_use = obj["key"]
        del obj["key"]
        remaining_key = next(iter(obj))
        obj = {key_to_use: obj[remaining_key]}
    for key, score_or_value in obj.items():
        if isinstance(score_or_value, dict):
            await parse_feedback(
                score_or_value,
                run,
                rule_id,
                run_id,
                feedback_inserts,
                evaluator_feedbacks_ids,
                depth + 1,
                error=error,
            )
        elif isinstance(score_or_value, list):
            for obj in score_or_value:
                if isinstance(obj, dict) or isinstance(obj, list):
                    await parse_feedback(
                        obj,
                        run,
                        rule_id,
                        run_id,
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        depth + 1,
                        error=error,
                    )
                else:
                    await parse_feedback(
                        {key: obj},
                        run,
                        rule_id,
                        run_id,
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        depth + 1,
                        error=error,
                    )
        else:
            try:
                payload = orjson.loads(
                    ORJSONSerializer.dumps(
                        schemas.FeedbackCreateSchemaInternal(
                            id=uuid4(),
                            run_id=run["id"],
                            key=key,
                            score=(
                                score_or_value
                                if not isinstance(score_or_value, str) and not error
                                else None
                            ),
                            value=(
                                score_or_value
                                if isinstance(score_or_value, str) and not error
                                else None
                            ),
                            feedback_source=schemas.AutoEvalFeedbackSource(
                                metadata={
                                    "rule_id": rule_id,
                                    "__run": {"run_id": str(run_id)},
                                }
                            ),
                            comment=comment if comment is not None else error,
                            error=error is not None,
                        )
                    )
                )
                feedback_inserts.append(
                    FeedbackInsert(
                        payload=payload,
                        trace_id=run["trace_id"],
                        session_id=run["session_id"],
                        start_time=run["start_time"],
                        redis=None,
                    )
                )
                evaluator_feedbacks_ids[str(run["id"])].append(payload["id"])
            except ValidationError as e:
                await logger.awarn(
                    "Evaluator produced invalid feedback for rule %s. Error: %s",
                    rule_id,
                    e,
                    exc_info=True,
                )


def merge_run_infos(first_run_infos):
    merged_first_run_infos = []

    # Merge every 3 dictionaries into one
    for i in range(0, len(first_run_infos), 3):
        merged_dict = {}
        for dict_item in first_run_infos[i : i + 3]:
            # Strip keys and values for each dictionary
            cleaned_dict = {
                k.strip("'"): v.strip("'") if isinstance(v, str) else v
                for k, v in dict_item.items()
            }
            merged_dict.update(cleaned_dict)
        merged_first_run_infos.append(merged_dict)

    return merged_first_run_infos


def get_run_thread_id(run: dict[str, Any]) -> str | None:
    if run["extra"].get("metadata"):
        return convert_to_string_if_present(
            run["extra"]["metadata"].get("session_id")
            or run["extra"]["metadata"].get("conversation_id")
            or run["extra"]["metadata"].get("thread_id")
        )
    return None


graph_interrupt_regex = re.compile(
    r"^GraphInterrupt(?:\(.*Interrupt.*\)|:\s*\[.*\])",
    re.MULTILINE | re.DOTALL | re.IGNORECASE,
)


def is_graph_interrupt(error: str | None) -> bool:
    if not error:
        return False
    return bool(graph_interrupt_regex.search(error))


def get_run_status(run: dict) -> str:
    if run["error"]:
        if is_graph_interrupt(run["error"]):
            return "interrupted"
        return "error"
    elif run["end_time"]:
        return "success"
    return "pending"


def _parse_time_with_timezone(time_str: str) -> datetime:
    """Parse ISO format time string and ensure it has timezone info (defaulting to UTC)."""
    dt = datetime.fromisoformat(time_str)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt


def calculate_amt_time_windows(
    start_time: str | None, end_time: str | None
) -> TimeWindows:
    """
    Calculate time windows for aggregated merge tree queries.

    Returns:
        TimeWindows containing all calculated time boundaries
    """
    start_time_dt = None
    end_time_dt = None
    amt_start_time_dt = None
    amt_end_time_dt = None
    trailing_edge_start_time_dt = None
    trailing_edge_end_time_dt = None
    leading_edge_start_time_dt = None
    leading_edge_end_time_dt = None

    if start_time:
        start_time_dt = _parse_time_with_timezone(start_time)
        # If start_time is on the hour mark, use it as agg start time
        if (
            start_time_dt.minute == 0
            and start_time_dt.second == 0
            and start_time_dt.microsecond == 0
        ):
            amt_start_time_dt = start_time_dt
            trailing_edge_start_time_dt = None
            trailing_edge_end_time_dt = None
        # Otherwise, calculate trailing edge and adjust agg start time
        else:
            amt_start_time_dt = start_time_dt.replace(
                minute=0, second=0, microsecond=0
            ) + timedelta(hours=1)
            trailing_edge_start_time_dt = start_time_dt
            trailing_edge_end_time_dt = (
                start_time_dt.replace(minute=0, second=0, microsecond=0)
                + timedelta(hours=1)
                - timedelta(microseconds=1)
            )

    if end_time:
        end_time_dt = _parse_time_with_timezone(end_time)
    else:
        end_time_dt = datetime.now(tz=timezone.utc)

    # If end_time is just before the hour mark, adjust agg end time
    if (
        end_time_dt.minute == 59
        and end_time_dt.second == 59
        and end_time_dt.microsecond == 999999
    ):
        amt_end_time_dt = end_time_dt.replace(minute=0, second=0, microsecond=0)
        leading_edge_start_time_dt = None
        leading_edge_end_time_dt = None
    # Otherwise, calculate leading edge and adjust agg end time
    else:
        amt_end_time_dt = end_time_dt.replace(
            minute=0, second=0, microsecond=0
        ) - timedelta(hours=1)
        leading_edge_start_time_dt = end_time_dt.replace(
            minute=0, second=0, microsecond=0
        )
        leading_edge_end_time_dt = end_time_dt

    # If there's no valid time range in the aggregated merge tree, reset to None
    if amt_end_time_dt and amt_start_time_dt and amt_end_time_dt < amt_start_time_dt:
        amt_start_time_dt = None
        amt_end_time_dt = None

    return TimeWindows(
        raw_start=start_time_dt,
        raw_end=end_time_dt,
        amt_start=amt_start_time_dt,
        amt_end=amt_end_time_dt,
        trailing_start=trailing_edge_start_time_dt,
        trailing_end=trailing_edge_end_time_dt,
        leading_start=leading_edge_start_time_dt,
        leading_end=leading_edge_end_time_dt,
    )


def _build_time_params(
    amt_start_time_dt: datetime | None,
    amt_end_time_dt: datetime | None,
    trailing_edge_start_time_dt: datetime | None,
    trailing_edge_end_time_dt: datetime | None,
    leading_edge_start_time_dt: datetime | None,
    leading_edge_end_time_dt: datetime | None,
) -> dict[str, str]:
    """Build time-based parameters for the queries."""
    new_params = {}

    if amt_start_time_dt:
        new_params["amt_start_time__gte"] = amt_start_time_dt.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        )
    if amt_end_time_dt:
        new_params["amt_start_time__lte"] = amt_end_time_dt.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        )
    if leading_edge_start_time_dt:
        new_params["leading_edge_start_time__gte"] = (
            leading_edge_start_time_dt.strftime("%Y-%m-%d %H:%M:%S.%f")
        )
    if leading_edge_end_time_dt:
        new_params["leading_edge_start_time__lte"] = leading_edge_end_time_dt.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        )
    if trailing_edge_start_time_dt:
        new_params["trailing_edge_start_time__gte"] = (
            trailing_edge_start_time_dt.strftime("%Y-%m-%d %H:%M:%S.%f")
        )
    if trailing_edge_end_time_dt:
        new_params["trailing_edge_start_time__lte"] = (
            trailing_edge_end_time_dt.strftime("%Y-%m-%d %H:%M:%S.%f")
        )

    return new_params


def _build_aggregated_query(
    stats_type: Literal["run_stats", "token_count_stats"],
    params: dict[str, Any],
    amt_start_time_dt: datetime | None,
    amt_end_time_dt: datetime | None,
) -> str | None:
    """Build the aggregated merge tree query."""
    if amt_start_time_dt is None and amt_end_time_dt is None:
        return None

    from app.models.tracer_sessions.expressions import (
        runs_stats_projection_agg,
        token_count_stats_projection_agg,
    )

    projection = (
        runs_stats_projection_agg[2:]
        if stats_type == "run_stats"
        else token_count_stats_projection_agg
    )

    # Build WHERE clause conditions - need to maintain original brace formatting
    where_conditions = []

    if params.get("is_root__eq") is not None and stats_type == "run_stats":
        where_conditions.append("is_root = {is_root__eq}")

    if params.get("reference_dataset_id__eq"):
        where_conditions.append("reference_dataset_id = {reference_dataset_id__eq}")

    if amt_start_time_dt is not None:
        where_conditions.append(
            "hour >= toStartOfHour(toDateTime64({amt_start_time__gte}, 6))"
        )

    where_conditions.extend(
        [
            "hour <= toStartOfHour(toDateTime64({amt_start_time__lte}, 6))",
            "session_id in {session_id__in}",
            "tenant_id = {tenant_id__eq}",
        ]
    )

    where_clause = " AND ".join(where_conditions)

    return f"""
        WITH {stats_type} AS (
            SELECT{projection}
            FROM runs_sessions_agg_hourly
            WHERE {where_clause}
        )
        SELECT {stats_type}.* FROM {stats_type}
    """


def _build_edge_queries(
    stats_type: Literal["run_stats", "token_count_stats"],
    select: str,
    runs_table_name: str,
    group_by_clause: str,
    skip_final: bool,
    runs_filtered_where: str,
    tokens_pushdown: str,
    leading_edge_start_time_dt: datetime | None,
    leading_edge_end_time_dt: datetime | None,
    trailing_edge_start_time_dt: datetime | None,
    trailing_edge_end_time_dt: datetime | None,
    is_all_time_query: bool,
) -> tuple[str | None, str | None]:
    """Build the leading and trailing edge queries."""
    if stats_type == "run_stats":
        base_query = f"""
        SELECT 
            {select}
            FROM {runs_table_name} {"FINAL" if (not group_by_clause and not skip_final) else ""}
            {"PREWHERE " + runs_filtered_where if runs_filtered_where else ""}
            {" AND start_time >= toDateTime64({start_time__gte}, 6)" if is_all_time_query else ""}
            {group_by_clause}
        """

        leading_edge_query = None
        trailing_edge_query = None

        if (
            leading_edge_start_time_dt is not None
            and leading_edge_end_time_dt is not None
        ):
            leading_edge_query = base_query.replace(
                "start_time__gte", "leading_edge_start_time__gte"
            ).replace("start_time__lte", "leading_edge_start_time__lte")

        if (
            trailing_edge_start_time_dt is not None
            and trailing_edge_end_time_dt is not None
        ):
            trailing_edge_query = base_query.replace(
                "start_time__gte", "trailing_edge_start_time__gte"
            ).replace("start_time__lte", "trailing_edge_start_time__lte")

    else:  # token_count_stats
        from app.models.runs.stats import exclude_expired_traces, token_count_projection

        base_query = f"""
            WITH token_counts as (
                SELECT{token_count_projection}
            FROM runs_token_counts {"FINAL" if not skip_final else ""}
            WHERE runs_token_counts.total_tokens < 4000000000 AND
                {tokens_pushdown}{exclude_expired_traces}
                {" AND start_time >= toDateTime64({start_time__gte}, 6)" if is_all_time_query else ""}
            GROUP BY id
            ),
        token_count_stats as (
            SELECT{select}
            FROM token_counts {group_by_clause}
            )
            SELECT token_count_stats.* FROM token_count_stats
        """

        leading_edge_query = None
        trailing_edge_query = None

        if (
            leading_edge_start_time_dt is not None
            and leading_edge_end_time_dt is not None
        ):
            leading_edge_query = base_query.replace(
                "start_time__gte", "leading_edge_start_time__gte"
            ).replace("start_time__lte", "leading_edge_start_time__lte")

        if (
            trailing_edge_start_time_dt is not None
            and trailing_edge_end_time_dt is not None
        ):
            trailing_edge_query = base_query.replace(
                "start_time__gte", "trailing_edge_start_time__gte"
            ).replace("start_time__lte", "trailing_edge_start_time__lte")

    return leading_edge_query, trailing_edge_query


def get_session_amt_merge_tree_queries_and_params(
    select: str,
    runs_table_name: str,
    group_by_clause: str,
    skip_final: bool,
    runs_filtered_where: str,
    params: dict[str, Any],
    stats_type: Literal["run_stats", "token_count_stats"],
    tokens_pushdown: str = "",
) -> tuple[str | None, str | None, str | None, dict[str, Any]]:
    """
    Get the aggregated merge tree query, the leading edge query,
    the trailing edge query, and the params for session stats.

    This function handles the complex logic of determining which time windows
    to query from the aggregated merge tree vs the edge queries based on
    the requested time range.

    Returns:
        A tuple of (amt_query, leading_edge_query, trailing_edge_query, new_params)
    """
    start_time = params.get("start_time__gte")
    end_time = params.get("start_time__lte")

    # Calculate all time windows
    time_windows = calculate_amt_time_windows(start_time, end_time)

    # Build time-based parameters
    new_params = _build_time_params(
        time_windows.amt_start,
        time_windows.amt_end,
        time_windows.trailing_start,
        time_windows.trailing_end,
        time_windows.leading_start,
        time_windows.leading_end,
    )

    # Build aggregated query
    amt_query = _build_aggregated_query(
        stats_type, params, time_windows.amt_start, time_windows.amt_end
    )

    # Build edge queries
    leading_edge_query, trailing_edge_query = _build_edge_queries(
        stats_type,
        select,
        runs_table_name,
        group_by_clause,
        skip_final,
        runs_filtered_where,
        tokens_pushdown,
        time_windows.leading_start,
        time_windows.leading_end,
        time_windows.trailing_start,
        time_windows.trailing_end,
        start_time is None,
    )

    return amt_query, leading_edge_query, trailing_edge_query, new_params
