import asyncio
import datetime
import uuid
from contextlib import asynccontextmanager
from typing import Any, Async<PERSON><PERSON>ator, Awai<PERSON>, Callable

import asyncpg
import pytest
import structlog
from elasticsearch.exceptions import NotFoundError as ElasticsearchNotFoundError
from lc_database import elasticsearch
from pydantic import BaseModel

from app import config
from app.api.auth.verify import internal_auth_request
from app.models.datasets.indexed.index import reindex_dataset, sync_dataset
from app.tests.utils import FreshTenantClient, fresh_tenant_client

logger = structlog.getLogger(__name__)


class FakeInputSchema(BaseModel):
    foo: str


@asynccontextmanager
async def served_ds_enabled_tenant_client(
    db_asyncpg: asyncpg.Connection, use_api_key: bool
) -> AsyncGenerator[FreshTenantClient, Any]:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        await db_asyncpg.execute(
            """
            UPDATE organizations
            SET config = config || '{"can_serve_datasets": true}'
            WHERE id = $1
            """,
            authed_client.auth.organization_id,
        )
        yield authed_client


async def wait_for_indexing_to_finish(
    served_dataset_id: uuid.UUID,
    expected_count: int,
    max_wait_seconds: int = 10,
    allow_index_non_existence: bool = False,
):
    for _ in range(max_wait_seconds):
        try:
            count_resp = await elasticsearch.client.count(
                index=f"test_served_dataset_{served_dataset_id}"
            )
        except ElasticsearchNotFoundError as e:
            if not allow_index_non_existence:
                raise e
            else:
                await logger.ainfo(
                    "Index does not exist", served_dataset_id=served_dataset_id
                )
                await asyncio.sleep(1)
                continue

        count = count_resp.get("count")
        if count == expected_count:
            await logger.ainfo("Indexing finished", served_dataset_id=served_dataset_id)
            return

        await logger.ainfo(
            f"Found {count} records. Waiting for {expected_count} total records.",
            served_dataset_id=served_dataset_id,
        )
        await asyncio.sleep(1)

    raise Exception(f"Indexing did not finish in {max_wait_seconds} seconds")


async def _create_example(
    langsmith_client,
    dataset_id: uuid.UUID,
    inputs: dict,
    outputs: dict,
    example_id: uuid.UUID | None = None,
    metadata: dict | None = None,
) -> dict:
    if not example_id:
        example_id = uuid.uuid4()

    example_json = {
        "created_at": datetime.datetime.now(tz=datetime.timezone.utc).isoformat(),
        "inputs": inputs,
        "outputs": outputs,
        "dataset_id": str(dataset_id),
        "id": str(example_id),
    }

    if metadata:
        example_json["metadata"] = metadata

    response = await langsmith_client.post(
        "/examples",
        json=example_json,
    )
    assert response.status_code == 200, response.json()
    return response.json()


async def _setup_dataset(
    langsmith_client, inputs_schema: dict | None = FakeInputSchema.model_json_schema()
) -> dict:
    req_json: dict[str, Any] = {
        "name": "test dataset " + str(uuid.uuid4()),
        "description": "A test dataset",
        "data_type": "kv",
    }
    if inputs_schema is not None:
        req_json["inputs_schema_definition"] = inputs_schema
    resp = await langsmith_client.post(
        "/datasets",
        json=req_json,
    )
    assert resp.status_code == 200
    return resp.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_input_validation_on_search(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        langsmith_client = authed_client.client
        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        response = await langsmith_client.post(
            "/examples",
            json={
                "created_at": "2021-01-01T00:00:00.000Z",
                "inputs": {"foo": "bar"},
                "outputs": {"output": "something"},
                "dataset_id": str(dataset_id),
            },
        )
        assert response.status_code == 200, response.json()

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()
        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 1)

        # No error on proper schema
        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "hello"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 1

        # 422 on bad schema
        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"bar": "hello"}, "limit": 5},
        )
        assert response.status_code == 422


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_return_right_number_of_results(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        langsmith_client = authed_client.client
        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        # exact match example
        matching_example = await _create_example(
            langsmith_client,
            uuid.UUID(dataset_id),
            {"foo": "bar"},
            {"output": "example 1"},
        )
        matching_example_id = matching_example["id"]

        # no match example 1
        non_matching_example_1 = await _create_example(
            langsmith_client,
            uuid.UUID(dataset_id),
            {"foo": "chicken"},
            {"output": "example 2"},
        )
        non_matching_example_id_1 = non_matching_example_1["id"]

        # no match example 2
        non_matching_example_2 = await _create_example(
            langsmith_client,
            uuid.UUID(dataset_id),
            {"foo": "shoes"},
            {"output": "example 3"},
        )
        non_matching_example_id_2 = non_matching_example_2["id"]

        await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )

        await wait_until_task_queue_empty(debug=True)  # type: ignore
        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 3)

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "bar"}, "limit": 2, "debug": True},
        )

        assert response.status_code == 200, f"response: {response.json()}"

        assert len(response.json()["examples"]) == 2
        assert response.json()["examples"][0]["outputs"]["output"] == "example 1"
        assert response.json()["examples"][0]["id"] == matching_example_id
        assert response.json()["examples"][1]["id"] in [
            non_matching_example_id_1,
            non_matching_example_id_2,
        ]
        assert response.json()["examples"][0]["debug_info"]["score"] > 0
        assert response.json()["examples"][1]["debug_info"]["score"] == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_invalid_output_schema_sync(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
        )

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        await sync_dataset(
            auth.auth.tenant_id, dataset_id, served_dataset_id=served_dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 1)

        bad_example = await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "baz"},
            # Invalid output to that would trigger sync error without flattening
            {"output": {"foo": "bar"}},
        )

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "baz"},
            # add another correct example in after the incorrect one
            {"output": "correct"},
        )

        await sync_dataset(
            auth.auth.tenant_id, dataset_id, served_dataset_id=served_dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 3)

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "baz"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 3
        for example in response.json()["examples"]:
            if example["id"] == bad_example["id"]:
                found_example = True
                assert example["outputs"]["output"] == {"foo": "bar"}
                break

        assert found_example


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_regenerate_index(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client
        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
        )

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        await sync_dataset(
            auth.auth.tenant_id, dataset_id, served_dataset_id=served_dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 1)

        # Have to get a valid auth from the backend. Technically wrong form of auth
        # to make this API request, but this will cause it to get the organization
        # config to be filled out properly
        service_auth = await internal_auth_request(auth.auth.tenant_id)

        await reindex_dataset(service_auth, dataset_id)

        await wait_for_indexing_to_finish(
            served_dataset_id, 1, allow_index_non_existence=True
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_regenerate_index_from_endpoint(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client
        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
        )

        # search for the example
        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "bar"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 0

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index/sync",
        )
        assert response.status_code == 200, response.json()
        await wait_for_indexing_to_finish(served_dataset_id, 1)

        # search for the example
        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "bar"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 1


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_overwrite_same_example(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client
        dataset = await _setup_dataset(langsmith_client)
        dataset_id = dataset["id"]

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()

        example_id = uuid.uuid4()

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
            example_id=example_id,
        )

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "somethingelse"},
            example_id=example_id,
        )

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "somethingthird"},
            example_id=example_id,
        )

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")
        await sync_dataset(
            auth.auth.tenant_id, dataset_id, served_dataset_id=served_dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 1)

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "bar"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 1
        assert response.json()["examples"][0]["outputs"]["output"] == "somethingthird"

        resp = await langsmith_client.delete(f"/examples/{example_id}")
        assert resp.status_code == 200

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "somethingreborn"},
            example_id=example_id,
        )

        # Create a second example just so we can wait for the right count
        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "unrelated"},
        )

        await sync_dataset(
            auth.auth.tenant_id, dataset_id, served_dataset_id=served_dataset_id
        )
        await wait_for_indexing_to_finish(served_dataset_id, 2)

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "bar"}, "limit": 5},
        )
        assert response.status_code == 200, response.json()
        assert len(response.json()["examples"]) == 2

        for example in response.json()["examples"]:
            if example["id"] == str(example_id):
                found_example = True
                assert example["outputs"]["output"] == "somethingreborn"
                break

        assert found_example


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_invalid_old_examples_do_not_stop_sync(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client
        dataset = await _setup_dataset(langsmith_client, inputs_schema=None)
        dataset_id = dataset["id"]

        example_id = uuid.uuid4()

        # Create an example with an invalid schema before we add schema validation
        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": ["a", "b", "bar"]},
            {"output": "something"},
            example_id=example_id,
        )

        # Ensure the schema is invalid by trying to update the dataset's schema
        resp = await langsmith_client.patch(
            f"/datasets/{dataset_id}",
            json={
                "inputs_schema_definition": FakeInputSchema.model_json_schema(),
            },
        )
        assert resp.status_code == 400, resp.json()

        # Remove the bad example
        resp = await langsmith_client.delete(f"/examples/{example_id}")
        assert resp.status_code == 200, resp.json()

        # Now add the schema after "fixing" the dataset
        resp = await langsmith_client.patch(
            f"/datasets/{dataset_id}",
            json={
                "inputs_schema_definition": FakeInputSchema.model_json_schema(),
            },
        )
        assert resp.status_code == 200, resp.json()

        # Add a valid example
        valid_ex = await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "baz"},
            {"output": "doesnt matter"},
        )

        response = await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )
        assert response.status_code == 200, response.json()

        await wait_until_task_queue_empty()

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        await wait_for_indexing_to_finish(served_dataset_id, 1)

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "doesnt matter"}, "limit": 5},
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 1
        assert search.json()["examples"][0]["outputs"]["output"] == "doesnt matter"

        # Ensure the last processed example
        index_info_resp = await langsmith_client.get(f"/datasets/{dataset_id}/index")
        assert index_info_resp.status_code == 200, index_info_resp.json()
        index_info = index_info_resp.json()

        index_last_updated = datetime.datetime.fromisoformat(
            index_info["last_updated_version"]
        )
        valid_ex_modified_at = datetime.datetime.fromisoformat(valid_ex["modified_at"])

        assert index_last_updated == valid_ex_modified_at


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_delete_within_same_index_call_is_adhered(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        resp = await langsmith_client.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "description": "A test dataset",
                "data_type": "kv",
                "inputs_schema_definition": FakeInputSchema.model_json_schema(),
            },
        )
        dataset_id = resp.json()["id"]
        assert resp.status_code == 200

        example_id_one = uuid.uuid4()
        example_id_two = uuid.uuid4()

        # Create an example with an invalid schema before we add schema validation
        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
            example_id=example_id_one,
        )

        await langsmith_client.delete(f"/examples/{example_id_one}")

        # Create another example just so we can wait for the right count
        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "baz"},
            {"output": "doesnt matter"},
            example_id=example_id_two,
        )

        await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )

        await wait_until_task_queue_empty()

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        await wait_for_indexing_to_finish(served_dataset_id, 1)

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "doesnt matter"}, "limit": 5},
        )
        assert search.status_code == 200, search.json()
        # should not include deleted example
        assert len(search.json()["examples"]) == 1
        assert search.json()["examples"][0]["outputs"]["output"] == "doesnt matter"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_filter_on_metadata(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with served_ds_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        resp = await langsmith_client.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "description": "A test dataset",
                "data_type": "kv",
                "inputs_schema_definition": FakeInputSchema.model_json_schema(),
            },
        )
        dataset_id = resp.json()["id"]
        assert resp.status_code == 200

        example_id_one = uuid.uuid4()
        example_id_two = uuid.uuid4()

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "bar"},
            {"output": "something"},
            example_id=example_id_one,
            metadata={
                "a": {
                    "nested": {
                        "key": "foo",
                    },
                    "key": "bar",
                }
            },
        )

        await _create_example(
            langsmith_client,
            dataset_id,
            {"foo": "baz"},
            {"output": "doesnt matter"},
            example_id=example_id_two,
            metadata={
                "a": {
                    "nested": {
                        "key": "baz",
                    },
                    "key": "bar",
                }
            },
        )

        await langsmith_client.post(
            f"/datasets/{dataset_id}/index",
            json={
                "tag": "latest",
            },
        )

        await wait_until_task_queue_empty()

        served_dataset_id = await db_asyncpg.fetchval(
            "SELECT id FROM served_datasets WHERE dataset_id = $1", dataset_id
        )
        if not served_dataset_id:
            raise Exception("Served dataset not found")

        await wait_for_indexing_to_finish(served_dataset_id, 2)

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={"inputs": {"foo": "doesnt matter"}, "limit": 5},
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 2

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={
                "inputs": {"foo": "doesnt matter"},
                "limit": 5,
                "filter": "eq('metadata.a.nested.key', 'baz')",
            },
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 1
        assert search.json()["examples"][0]["id"] == str(example_id_two)

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={
                "inputs": {"foo": "doesnt matter"},
                "limit": 5,
                "filter": "neq(metadata.a.nested.key, baz)",
            },
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 1
        assert search.json()["examples"][0]["id"] == str(example_id_one)

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={
                "inputs": {"foo": "doesnt matter"},
                "limit": 5,
                "filter": "or(eq(metadata.a.nested.key, foo), eq(metadata.a.nested.key, baz))",
            },
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 2

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={
                "inputs": {"foo": "doesnt matter"},
                "limit": 5,
                "filter": "and(eq(metadata.a.nested.key, foo), eq(metadata.a.nested.key, bar))",
            },
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 0

        search = await langsmith_client.post(
            f"/datasets/{dataset_id}/search",
            json={
                "inputs": {"foo": "doesnt matter"},
                "limit": 5,
                "filter": "and(eq(metadata.a.key, bar), eq(metadata.a.nested.key, baz))",
            },
        )
        assert search.status_code == 200, search.json()
        assert len(search.json()["examples"]) == 1
        assert search.json()["examples"][0]["id"] == str(example_id_two)
