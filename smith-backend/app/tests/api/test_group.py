"""Test correct behavior of conversation view"""

import math
from typing import Any, Awaitable, Callable
from uuid import UUID, uuid4

import pytest
from httpx import AsyncClient

from app import config
from app.api.auth.schemas import AuthInfo
from app.tests.utils import random_lower_string

pytestmark = [
    pytest.mark.anyio,
    pytest.mark.skipif(
        config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
    ),
]


async def test_list_conversations(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    conversation_id = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 1",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"conversation_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "what's the weather in SF"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )

    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 2",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "foo": "bar",
                # use thread_id instead of conversation_id
                "metadata": {"thread_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "is this hot or is this cold?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "This is quite typical of SF weather",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "This is quite typical of SF weather",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 50,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # test with and without start time
    for start_time in [None, "2023-05-01T00:00:00.571809Z"]:
        # test before and after cutoff date
        response = await http_tenant_one.post(
            "/runs/group",
            json={
                "session_id": str(tenant_one_tracer_session_id),
                "group_by": "conversation",
                "start_time": start_time if start_time else None,
            },
        )
        assert response.status_code == 200
        json = response.json()

        assert len(json["groups"]) >= 1, (
            f"Groups should be >= 1 for start_time: {start_time}"
        )
        assert json["total"] >= 1, f"Total should be >= 1 for start_time: {start_time}"

        group = next(
            (g for g in json["groups"] if g["group_key"] == str(conversation_id)),
            None,
        )
        assert group is not None
        assert group["count"] == 2
        assert group["min_start_time"] == "2023-05-05T05:13:24.571809"
        assert group["max_start_time"] == "2023-05-05T05:15:24.571809"
        assert group["first_inputs"] == "human: what's the weather in SF"
        assert group["last_outputs"] == "ai: This is quite typical of SF weather"
        assert group["total_tokens"] == 28 + 30
        assert group["feedback_stats"]["foo"]["values"] == {
            "blue": 2,
            "green": 1,
        }
        assert group["feedback_stats"]["foo"]["avg"] == 50.0
        assert math.isclose(
            group["feedback_stats"]["foo"]["stdev"], 40.************, rel_tol=1e-9
        )
        assert group["feedback_stats"]["foo"]["n"] == 3


async def test_list_conversations_filtered(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    conversation_id = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 1",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"conversation_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "what's the weather in SF"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )

    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 2",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "foo": "bar",
                # use thread_id instead of conversation_id
                "metadata": {"thread_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "is this hot or is this cold?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "This is quite typical of SF weather",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "This is quite typical of SF weather",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 50,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # test with and without start time
    for start_time in [None, "2023-05-01T00:00:00.571809Z"]:
        # test before and after cutoff date
        response = await http_tenant_one.post(
            "/runs/group",
            json={
                "session_id": str(tenant_one_tracer_session_id),
                "group_by": "conversation",
                "start_time": start_time if start_time else None,
                "filter": 'and(eq(name, "Run 1"))',
            },
        )
        assert response.status_code == 200
        json = response.json()

        assert len(json["groups"]) >= 1, (
            f"Groups should be >= 1 for start_time: {start_time}"
        )
        assert json["total"] >= 1, f"Total should be >= 1 for start_time: {start_time}"

        group = next(
            (g for g in json["groups"] if g["group_key"] == str(conversation_id)),
            None,
        )
        assert group is not None
        assert group["count"] == 2
        assert group["min_start_time"] == "2023-05-05T05:13:24.571809"
        assert group["max_start_time"] == "2023-05-05T05:15:24.571809"
        assert group["first_inputs"] == "human: what's the weather in SF"
        assert group["last_outputs"] == "ai: This is quite typical of SF weather"
        assert group["total_tokens"] == 28 + 30
        assert group["feedback_stats"]["foo"]["values"] == {
            "blue": 2,
            "green": 1,
        }
        assert group["feedback_stats"]["foo"]["avg"] == 50.0
        assert math.isclose(
            group["feedback_stats"]["foo"]["stdev"], 40.************, rel_tol=1e-9
        )
        assert group["feedback_stats"]["foo"]["n"] == 3

    # test filter does not return any groups
    response = await http_tenant_one.post(
        "/runs/group",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'and(eq(name, "SomeOtherRun"))',
        },
    )
    assert response.status_code == 200
    json = response.json()
    assert len(json["groups"]) == 0
    assert json["total"] == 0


async def test_list_conversations_search_filter(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    conversation_id = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()

    # Create run with langchain content
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LangChain Agent",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"conversation_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "How do I use langchain for RAG?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "LangChain provides excellent tools for RAG implementation using vector stores and retrievers",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "LangChain provides excellent tools for RAG implementation using vector stores and retrievers",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 15,
                    "output_tokens": 25,
                    "total_tokens": 40,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    # Create run without langchain content
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Generic Chat",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"thread_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "What's the weather today?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "I don't have access to current weather data",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "I don't have access to current weather data",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 8,
                    "output_tokens": 12,
                    "total_tokens": 20,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "quality",
            "score": 90,
            "value": "excellent",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "quality",
            "score": 70,
            "value": "good",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # Test filtering with search("langchain")
    response = await http_tenant_one.post(
        "/runs/group",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'search("langchain")',
        },
    )
    assert response.status_code == 200
    json = response.json()

    # Should find groups containing langchain content
    assert len(json["groups"]) >= 1
    assert json["total"] >= 1

    # Find the conversation group
    group = next(
        (g for g in json["groups"] if g["group_key"] == str(conversation_id)),
        None,
    )
    assert group is not None

    # Verify the group contains both runs (grouped by conversation)
    assert group["count"] == 2
    assert group["total_tokens"] == 40 + 20  # Combined tokens from both runs

    # Verify feedback stats are aggregated correctly
    assert "quality" in group["feedback_stats"]
    assert group["feedback_stats"]["quality"]["n"] == 2
    assert group["feedback_stats"]["quality"]["avg"] == 80.0  # (90 + 70) / 2
    assert group["feedback_stats"]["quality"]["values"] == {
        "excellent": 1,
        "good": 1,
    }

    # Test that non-matching search returns no results
    response = await http_tenant_one.post(
        "/runs/group",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'search("nonexistent")',
        },
    )
    assert response.status_code == 200
    json = response.json()
    assert len(json["groups"]) == 0
    assert json["total"] == 0


async def test_group_stats_with_search_filter(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    http_tenant_one, _ = fresh_tenant

    conversation_id_1 = uuid4()
    conversation_id_2 = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    tenant_one_tracer_session_id = response.json()["id"]

    # Create first conversation with langchain content
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LangChain RAG Pipeline",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "metadata": {"conversation_id": str(conversation_id_1)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "How to implement RAG with langchain?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "LangChain provides excellent RAG capabilities through vector stores",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "LangChain provides excellent RAG capabilities through vector stores",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 20,
                    "output_tokens": 30,
                    "total_tokens": 50,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    # Create second run in same conversation
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LangChain Follow-up",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "metadata": {"thread_id": str(conversation_id_1)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "input": ["human", "What about vector embeddings in langchain?"]
            },
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "LangChain supports multiple embedding providers for vector search",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "LangChain supports multiple embedding providers for vector search",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 15,
                    "output_tokens": 25,
                    "total_tokens": 40,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Create third run in different conversation without langchain content
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Weather Query",
            "start_time": "2023-05-05T05:17:24.571809",
            "end_time": "2023-05-05T05:18:24.571809",
            "extra": {
                "metadata": {"conversation_id": str(conversation_id_2)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "What's the weather like?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "I don't have access to current weather data",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "I don't have access to current weather data",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 15,
                    "total_tokens": 25,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "helpfulness",
            "score": 95,
            "value": "very_helpful",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "helpfulness",
            "score": 85,
            "value": "helpful",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # Test /runs/group/stats with search filter for "langchain"
    response = await http_tenant_one.post(
        "/runs/group/stats",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'search("langchain")',
        },
    )
    assert response.status_code == 200
    stats = response.json()

    # Verify stats structure and values
    assert "run_count" in stats
    assert "group_count" in stats
    assert "total_tokens" in stats
    assert "prompt_tokens" in stats
    assert "completion_tokens" in stats

    # Should find 2 runs in 1 conversation group that contain "langchain"
    assert stats["run_count"] == 2
    assert stats["group_count"] == 1
    assert stats["total_tokens"] == 90  # 50 + 40 tokens from langchain runs
    assert stats["prompt_tokens"] == 35  # 20 + 15 input tokens
    assert stats["completion_tokens"] == 55  # 30 + 25 output tokens

    # Test /runs/group/stats without filter to get all stats
    response = await http_tenant_one.post(
        "/runs/group/stats",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
        },
    )
    assert response.status_code == 200
    all_stats = response.json()

    # Should find all 3 runs in 2 conversation groups
    assert all_stats["run_count"] == 3
    assert all_stats["group_count"] == 2
    assert all_stats["total_tokens"] == 115  # 50 + 40 + 25 tokens from all runs
    assert all_stats["prompt_tokens"] == 45  # 20 + 15 + 10 input tokens
    assert all_stats["completion_tokens"] == 70  # 30 + 25 + 15 output tokens

    # Test with non-matching search filter
    response = await http_tenant_one.post(
        "/runs/group/stats",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'search("nonexistent")',
        },
    )
    assert response.status_code == 200
    empty_stats = response.json()

    # Should return zero stats for non-matching filter
    assert empty_stats["run_count"] == 0
    assert empty_stats["group_count"] == 0
    assert empty_stats["total_tokens"] is None or empty_stats["total_tokens"] == 0
