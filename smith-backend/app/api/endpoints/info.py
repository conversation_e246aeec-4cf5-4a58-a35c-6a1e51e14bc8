"""Endpoints for LangSmith server info."""

from datetime import datetime, timezone

from fastapi import Response
from lc_config.utils import is_payment_enabled
from lc_database import api_router
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

import app
from app import schemas
from app.config import settings, shared_settings
from app.license.validation import decode_license_key
from app.utils import arun_in_executor

router = api_router.TrailingSlashRouter()


@router.get("")
async def get_server_info(response: Response) -> schemas.InfoGetResponse:
    """Get information about the current deployment of LangSmith."""
    # Don't send exp time for v2 license keys yet till we move this to go
    expiration_timestamp = None
    if (
        "local" in shared_settings.LANGCHAIN_ENV
        and not shared_settings.LANGSMITH_LICENSE_KEY.startswith("lcl_")
    ):
        decoded_license = await arun_in_executor(
            decode_license_key, shared_settings.LANGSMITH_LICENSE_KEY
        )
        expiration_timestamp = datetime.fromtimestamp(
            decoded_license.get("exp"), tz=timezone.utc
        )

        customer_info = schemas.CustomerInfo(
            customer_id=decoded_license.get("sub"),
            customer_name=decoded_license.get("customer_name")
            if decoded_license.get("customer_name")
            else "",
        )

    if settings.INFO_CACHE_MAX_AGE_SECONDS > 0:
        response.headers["Cache-Control"] = (
            f"public, max-age={settings.INFO_CACHE_MAX_AGE_SECONDS}"
        )
    return schemas.InfoGetResponse(
        version=app.__version__,
        license_expiration_time=expiration_timestamp,
        instance_flags={
            "generate_ai_query_enabled": True,
            "org_creation_disabled": shared_settings.FF_ORG_CREATION_DISABLED,
            "personal_orgs_disabled": shared_settings.FF_PERSONAL_ORGS_DISABLED,
            "show_ttl_ui": shared_settings.FF_TRACE_TIERS_ENABLED,
            "trace_tier_duration_days": {
                k: v // (60 * 60 * 24)
                for k, v in shared_settings.TRACE_TIER_TTL_DURATION_SEC_MAP.items()
            },
            "search_enabled": shared_settings.FF_CH_SEARCH_ENABLED,
            "experimental_search_enabled": shared_settings.QUICKWIT_SEARCH_ENABLED,
            "workspace_scope_org_invites": settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED,
            "s3_storage_enabled": settings.FF_BLOB_STORAGE_ENABLED,
            "blob_storage_enabled": settings.FF_BLOB_STORAGE_ENABLED,
            "blob_storage_engine": settings.BLOB_STORAGE_ENGINE,
            "examples_multipart_enabled": True,
            "dataset_examples_multipart_enabled": True,
            "payment_enabled": is_payment_enabled(),
            "zstd_compression_enabled": True,
            "playground_auth_bypass_enabled": settings.PLAYGROUND_BYPASS_AUTH_ENABLED,
        },
        customer_info=customer_info,
    )


@router.get("/health")
async def get_health_info() -> schemas.HealthInfoGetResponse:
    """Get health information about the current deployment of LangSmith."""
    if settings.IS_SELF_HOSTED:
        async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
            usage_percent = await ch.fetch(
                "disk_usage",
                """
                select
                    hostname(),
                    free_space,
                    total_space,
                    round(100.0*free_space/total_space,1) as pct_free
                from clusterAllReplicas(default,system.disks)
                where path is not null
                and type != 'ObjectStorage'
                SETTINGS skip_unavailable_shards = 1
                """,
            )
            return schemas.HealthInfoGetResponse(
                clickhouse_disk_free_pct=usage_percent[0]["pct_free"],
            )
    else:
        # For non-self-hosted deployments, don't report ClickHouse disk usage
        return schemas.HealthInfoGetResponse(
            clickhouse_disk_free_pct=100.0,  # Default to 100% free space for non-self-hosted deployments
        )
